package com.salesmobile.ui.inputproduct;

import com.salesmobile.config.ParametrosConf;
import android.Manifest;
import android.app.AlertDialog;
import android.app.Dialog;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.SharedPreferences;
import android.content.pm.PackageManager;
import android.graphics.Color;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.HandlerThread;
import android.text.Html;
import android.text.TextUtils;
import android.util.DisplayMetrics;
import android.util.Log;
import android.util.TypedValue;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.inputmethod.EditorInfo;
import android.widget.ArrayAdapter;
import android.widget.Button;
import android.widget.EditText;
import android.widget.GridLayout;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.Spinner;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;

import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.DialogFragment;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.lifecycle.ViewModelProvider;
import androidx.navigation.NavController;
import androidx.navigation.Navigation;

import com.google.android.material.snackbar.Snackbar;

//import com.salesmobile.Manifest;
import com.salesmobile.AppDatabase;
import com.salesmobile.Configuracion;
import com.salesmobile.R;
import com.salesmobile.TokenManager;
import com.salesmobile.TokenManager2;
import com.salesmobile.TokenRequest;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.IOException;
import java.nio.Buffer;
import java.text.NumberFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.FormBody;
import okhttp3.Interceptor;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import okhttp3.MediaType;
import android.content.Context;


import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;


public class InputProductFragment extends Fragment implements TokenRequest.TokenRequestListener {
    private SharedViewModel viewModel;
    private String token; // Variable para almacenar el token
    private EditText editTextProductCode;
    private Spinner numberSpinner;
    private Button buttonAddProduct;
    private Button buttonAceptar;
    private String productCode;
    public TextView textDescripcion;
    public TextView textPrecio;
    private RecyclerView recyclerViewSizes; // RecyclerView para los botones de tallas
    private SizeButtonAdapter sizeButtonAdapter;
    private String selectedSize;
    public TextView textViewTalla;
    public TextView textView2; // TextView para "Cantidad:"
    private long tokenExpirationTime; // Almacena el tiempo de expiración del token
    private boolean isTokenRequestInProgress = false; // Evita solicitudes redundantes
    private Button buttonScanBarcode; // Botón para escanear
    private static final int REQUEST_CAMERA_PERMISSION = 1001; // Puedes usar cualquier valor único
    private final HandlerThread handlerThread = new HandlerThread("InputProductFragmentThread");
    private String baseUrl;
    private String baseUrl2;
    private String CodEmpresa;
    private String CodSucursal;
    private String fechaFormateada;
    private String vigencia;
    private String mejorVigencia;
    private ProgressBar progressBarInput;
    private volatile double cotizacion = 1.0;
    private List<Bien> bienesList;
    private String soloconsulta;


    private void showOkDialog() {
        Handler handler = new Handler(handlerThread.getLooper());

        Runnable runnable = new Runnable() {
            @Override
            public void run() {
                requireActivity().runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        AlertDialog.Builder builder = new AlertDialog.Builder(requireContext());
                        builder.setTitle("Confirmación")
                                .setMessage("Producto Inexistente")
                                .setPositiveButton("Aceptar", new DialogInterface.OnClickListener() {
                                    @Override
                                    public void onClick(DialogInterface dialog, int which) {
                                        // Do nothing
                                    }
                                })
                                .show();
                    }
                });
            }
        };

        handler.post(runnable);
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        viewModel = new ViewModelProvider(requireActivity()).get(SharedViewModel.class);
        handlerThread.start();

        // Recuperar el valor de la cotizacion del dia
        SharedPreferences prefs = requireContext().getSharedPreferences("ConfigDB", Context.MODE_PRIVATE);
        cotizacion = prefs.getFloat("valor1_indices", 1f); // 0f es el valor por defecto
        soloconsulta = prefs.getString("soloconsulta", "N");

        // Usar el token existente o solicitar uno nuevo
        TokenManager tokenManager = TokenManager.getInstance();
        tokenManager.requestToken(requireContext(), () -> {
            Log.d("Token", "Token recibido: " + tokenManager.getToken());
        });
    }



    private void obtenerBaseUrl() {
        Executors.newSingleThreadExecutor().execute(() -> {
            AppDatabase db = AppDatabase.getInstance(requireContext());
            Configuracion configuracion = db.configuracionDao().obtenerConfiguracion();
            String url = configuracion != null ? configuracion.getBaseUrl() : ParametrosConf.APPMOVIL_BASE_URL;
            String url2 = configuracion != null ? configuracion.getBaseUrl2() : ParametrosConf.APPMOVIL_BASE_URL;
            String empresa = configuracion != null ? configuracion.getCodigoEmpresa() : "1";
            String sucursal = configuracion != null ? configuracion.getCodigoSucursal() : "1";

            // Actualizar la UI en el hilo principal
            requireActivity().runOnUiThread(() -> {
                baseUrl = url;
                baseUrl2 = url2;
                CodEmpresa = empresa;
                CodSucursal = sucursal;
            });
        });
    }


    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_inputproduct, container, false);
        editTextProductCode = view.findViewById(R.id.editTextProductCode);
        buttonAddProduct = view.findViewById(R.id.buttonAddProduct);
        buttonAceptar = view.findViewById(R.id.buttonAceptar);
        Button btnShowCart = view.findViewById(R.id.btnShowCart);
        numberSpinner = view.findViewById(R.id.numberSpinner);
        textDescripcion = view.findViewById(R.id.textDescripcion);
        textPrecio = view.findViewById(R.id.textPrecio);
        recyclerViewSizes = view.findViewById(R.id.recyclerViewSizes);
        textViewTalla = view.findViewById(R.id.textViewTalla);
        textView2 = view.findViewById(R.id.textView2);

        // Estado inicial: carrito oculto, agregar oculto, cantidad oculta
        btnShowCart.setVisibility(View.GONE);
        buttonAddProduct.setVisibility(View.GONE);
        textView2.setVisibility(View.GONE);
        numberSpinner.setVisibility(View.GONE);

        // Configurar RecyclerView para tallas
        sizeButtonAdapter = new SizeButtonAdapter();
        recyclerViewSizes.setLayoutManager(new GridLayoutManager(getContext(), 4));
        recyclerViewSizes.setAdapter(sizeButtonAdapter);
        progressBarInput = view.findViewById(R.id.progressBarInput);
        obtenerBaseUrl();
        // Obtener la fecha actual del sistema
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd-MM-yyyy");
        fechaFormateada = LocalDate.now().format(formatter);

        // Configurar el botón de escanear para mostrar el modal
        Button buttonScanBarcode = view.findViewById(R.id.buttonScanBarcode);
        buttonScanBarcode.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (ContextCompat.checkSelfPermission(requireContext(), Manifest.permission.CAMERA) == PackageManager.PERMISSION_GRANTED) {
                    showBarcodeScannerModal();
                } else {
                    ActivityCompat.requestPermissions(requireActivity(), new String[]{Manifest.permission.CAMERA}, REQUEST_CAMERA_PERMISSION);
                }
            }
        });



        //Inicializado oculto el textview de los tamaños
        textViewTalla.setVisibility(View.GONE);

        // Configuración del Spinner
        String[] numbers = getResources().getStringArray(R.array.numbers_array);
        ArrayAdapter<String> adapter = new ArrayAdapter<>(requireContext(), R.layout.spinner_selected_item_layout, numbers);
        adapter.setDropDownViewResource(R.layout.spinner_dropdown_item_layout);
        numberSpinner.setAdapter(adapter);

        // Verificar si ya hay tallas cargadas y mostrar el textViewTalla si es necesario
        checkAndShowTallaIfNeeded();

        buttonAceptar.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                // Validar que el campo no esté vacío
                productCode = editTextProductCode.getText().toString().trim();
                if (productCode.isEmpty()) {
                    Toast.makeText(requireContext(), "Por favor ingrese un código de producto", Toast.LENGTH_LONG).show();
                    return;
                }

                TokenManager tokenManager = TokenManager.getInstance();
                String token = tokenManager.getToken();

                if (token == null) {
                    Log.e("buttonAceptar", "Token no disponible");
                    return;
                }
                makeApiRequestWithToken(token);
                fetchPrice(new OkHttpClient(), token, productCode);
            }
        });

        buttonAddProduct.setOnClickListener(new View.OnClickListener() {

            @Override
            public void onClick(View v) {
                if (soloconsulta.equals("S")){
                    Toast.makeText(requireContext(), "No tiene permisos para agregar productos", Toast.LENGTH_LONG).show();
                } else {
                    addProduct();
                }
            }
        });

        btnShowCart.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                showCart(view);
            }
        });

        // Observer para mostrar/ocultar botón carrito según contenido
        viewModel.getCartProducts().observe(getViewLifecycleOwner(), productos -> {
            if (productos != null && !productos.isEmpty()) {
                btnShowCart.setVisibility(View.VISIBLE);
            } else {
                btnShowCart.setVisibility(View.GONE);
            }
        });

        return view;
    }

    private void checkAndShowTallaIfNeeded() {
        // Verificar si hay botones de tallas en el RecyclerView o GridLayout
        if ((sizeButtonAdapter != null && sizeButtonAdapter.getItemCount() > 0) ||
            (recyclerViewSizes != null && recyclerViewSizes.getChildCount() > 0)) {
            textViewTalla.setVisibility(View.VISIBLE);
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        if (requestCode == REQUEST_CAMERA_PERMISSION) {
            if (grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                showBarcodeScannerModal();
            } else {
                Toast.makeText(requireContext(), "Permiso de cámara denegado", Toast.LENGTH_SHORT).show();
            }
        }
    }

    private void showBarcodeScannerModal() {
        BarcodeScannerDialogFragment scannerDialog = BarcodeScannerDialogFragment.newInstance();
        scannerDialog.setOnBarcodeScannedListener(new BarcodeScannerDialogFragment.OnBarcodeScannedListener() {
            @Override
            public void onBarcodeScanned(String barcode) {
                editTextProductCode.setText(barcode);

                // Ejecutar el evento buscar completo
                TokenManager tokenManager = TokenManager.getInstance();
                String token = tokenManager.getToken();

                if (token != null) {
                    makeApiRequestWithToken(token);
                    productCode = barcode.trim();
                    fetchPrice(new OkHttpClient(), token, productCode);
                } else {
                    Log.e("BarcodeScan", "Token no disponible");
                }

                fetchCodigoMadre(barcode);
            }
        });
        scannerDialog.show(getParentFragmentManager(), "BarcodeScannerDialog");
    }




    private boolean isTokenValid(String token) {
        return token != null;
    }

    private void addProduct() {
        String cantidadSeleccionada = (String) numberSpinner.getSelectedItem();
        productCode = editTextProductCode.getText().toString();
        String precioText = textPrecio.getText().toString();
        String descripcion = textDescripcion.getText().toString();

        if (!productCode.isEmpty() && selectedSize != null && !precioText.isEmpty()) {
            try {
                // 1. Obtener el stock disponible para la talla seleccionadasa
                double stockDisponible = getStockForSelectedSize();
                int cantidad = Integer.parseInt(cantidadSeleccionada);

                // 2. Validar que la cantidad no supere el stock disponible
                if (cantidad > stockDisponible) {
                    showStockExceededDialog(stockDisponible);
                    return; // Salir del método sin agregar el producto
                }

                // 3. Continuar con el procesamiento normal si la validación pasa
                String cleanText = Html.fromHtml(precioText, Html.FROM_HTML_MODE_LEGACY).toString();
                String[] partes = cleanText.split("Gs\\.");
                if (partes.length < 2) {
                    throw new NumberFormatException("Formato de precio no válido");
                }
                String precioFinalStr = partes[partes.length - 1];
                String soloNumeros = precioFinalStr.replaceAll("[^0-9]", "");
                double precioFinal = Double.parseDouble(soloNumeros);

                Product product = new Product(
                        productCode,
                        descripcion,
                        precioFinal,
                        selectedSize,
                        cantidad
                );

                viewModel.addProductToCart(product);
                showSnackbar("✔ " + descripcion + " agregado al carrito");

                // Limpiar campos
                clearProductCodeField();
                clearDescripcionField();
                cleartextPrecioField();
                numberSpinner.setSelection(0); // Resetear cantidad a 1
                if (sizeButtonAdapter != null) {
                    sizeButtonAdapter.setSizeItems(new ArrayList<>());
                    adjustRecyclerViewHeight(0); // Encoge el RecyclerView cuando no hay elementos
                }
                textViewTalla.setVisibility(View.GONE);
                // Ocultar botón agregar después de limpiar campos
                buttonAddProduct.setVisibility(View.GONE);

            } catch (NumberFormatException e) {
                Toast.makeText(requireContext(), "Error: El precio no es válido", Toast.LENGTH_LONG).show();
                Log.e("AGREGAR_CARRITO", "Error al parsear: " + precioText, e);
            }
        } else {
            Toast.makeText(requireContext(), "❌ Complete todos los campos", Toast.LENGTH_SHORT).show();
        }
    }

    // Método auxiliar para parsear precios en formato Paraguay
    private double parsePrecioParaguayo(String precioStr) {
        try {
            // Eliminar todos los puntos (separador de miles en PY)
            String withoutDots = precioStr.replace(".", "");

            // Reemplazar coma decimal por punto (para parseo correcto)
            String normalized = withoutDots.replace(",", ".");

            return Double.parseDouble(normalized);
        } catch (NumberFormatException e) {
            Log.e("PriceParser", "Error parsing price: " + precioStr, e);
            throw new NumberFormatException("Formato de precio inválido: " + precioStr);
        }
    }
    // Limpio las variables despues de agregar el producto al carrito
    private void clearProductCodeField() {
        editTextProductCode.setText("");
    }
    private void clearDescripcionField() {
        textDescripcion.setText("");
    }
    private void cleartextPrecioField() {
        textPrecio.setText("");
    }


    private void makeApiRequestWithToken(String token) {
        progressBarInput.setVisibility(View.VISIBLE);

        if (token == null) {
            Log.e("makeApiRequestWithToken", "Token no disponible");
            progressBarInput.setVisibility(View.GONE);
            return;
        }

        productCode = editTextProductCode.getText().toString().trim();
        if (productCode.isEmpty()) {
            progressBarInput.setVisibility(View.GONE);
            showOkDialog();
            return;
        }

        RequestBody requestBody = buildRequestBody(productCode);
        OkHttpClient client = new OkHttpClient.Builder()
                .connectTimeout(15, TimeUnit.SECONDS)
                .readTimeout(15, TimeUnit.SECONDS)
                .writeTimeout(15, TimeUnit.SECONDS)
                .build();

        Request request = new Request.Builder()
                .url(baseUrl + "/api/CONSULTAGRAL/Stock")
                .header("Authorization", "Bearer " + token)
                .post(requestBody)
                .build();

        client.newCall(request).enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                requireActivity().runOnUiThread(() -> {
                    progressBarInput.setVisibility(View.GONE);
                    showErrorDialog("Error de conexión: " + e.getMessage());
                });
            }

            @Override
            public void onResponse(Call call, Response response) throws IOException {
                try {
                    String responseData = response.body().string();
                    Log.d("API_STOCK_RESPONSE", responseData);

                    if (!response.isSuccessful()) {
                        handleApiError("Error en la respuesta: " + response.code(), responseData);
                        return;
                    }

                    processStockResponse(responseData);

                } catch (Exception e) {
                    requireActivity().runOnUiThread(() -> {
                        progressBarInput.setVisibility(View.GONE);
                        //showErrorDialog("Error procesando respuesta: " + e.getMessage());
                        Log.d("API_STOCK_RESPONSE_ERROR", "Error procesando respuesta: " + e.getMessage());
                    });
                }
            }
        });
    }

    private void processStockResponse(String responseData) throws JSONException {
        try {
            JSONObject jsonResponse = new JSONObject(responseData);

            if (responseData.contains("\"BIENES\":null") || !jsonResponse.has("BIENES") || jsonResponse.isNull("BIENES")) {
                showNoResults();
                return;
            }

            JSONObject bienesObject = jsonResponse.getJSONObject("BIENES");
            JSONArray bienesArray;

            if (bienesObject.get("Bien") instanceof JSONArray) {
                bienesArray = bienesObject.getJSONArray("Bien");
            } else {
                bienesArray = new JSONArray();
                bienesArray.put(bienesObject.getJSONObject("Bien"));
            }

            if (bienesArray.length() == 0) {
                showNoResults();
                return;
            }

            // Crear nueva lista y asignarla a la variable de clase
            List<Bien> nuevaLista = new ArrayList<>();

            for (int i = 0; i < bienesArray.length(); i++) {
                try {
                    JSONObject bienJson = bienesArray.getJSONObject(i);
                    Bien bien = new Bien(
                            bienJson.getString("Codigo"),
                            bienJson.optString("Descripcion", ""),
                            bienJson.optString("DescripcionLarga", ""),
                            processStocks(bienJson.optJSONObject("Stocks"))
                    );
                    nuevaLista.add(bien);
                    Log.d("BIEN_ADDED", "Añadido: " + bien.getCodigo());
                } catch (JSONException e) {
                    Log.e("PROCESS_BIEN", "Error procesando item " + i, e);
                }
            }

            // Asignar la nueva lista a la variable de clase
            this.bienesList = nuevaLista;
            Log.d("BIENES_LOADED", "Total bienes cargados: " + this.bienesList.size());

            // Actualizar UI en el hilo principal
            requireActivity().runOnUiThread(() -> {
                progressBarInput.setVisibility(View.GONE);
                if (this.bienesList != null && !this.bienesList.isEmpty()) {
                    updateUIWithBienes(this.bienesList);
                } else {
                    showNoResults();
                }
            });

        } catch (Exception e) {
            Log.e("PROCESS_STOCK", "Error procesando respuesta", e);
            requireActivity().runOnUiThread(this::showNoResults);
        }
    }

    private void showNoResults() {
        requireActivity().runOnUiThread(() -> {
            progressBarInput.setVisibility(View.GONE);
            if (sizeButtonAdapter != null) {
                sizeButtonAdapter.setSizeItems(new ArrayList<>());
                adjustRecyclerViewHeight(0); // Encoge el RecyclerView cuando no hay elementos
            }
            // Ocultar elementos de cantidad y botón agregar cuando no hay resultados
            textView2.setVisibility(View.GONE);
            numberSpinner.setVisibility(View.GONE);
            buttonAddProduct.setVisibility(View.GONE);
            Toast.makeText(getContext(), "No se encontraron resultados", Toast.LENGTH_LONG).show();
        });
    }

    private Map<String, Double> processStocks(JSONObject stocksJson) throws JSONException {
        Map<String, Double> stocksMap = new HashMap<>();
        if (stocksJson == null) return stocksMap;

        if (stocksJson.has("Stock")) {
            Object stockData = stocksJson.get("Stock");

            if (stockData instanceof JSONArray) {
                JSONArray stockArray = (JSONArray) stockData;
                for (int i = 0; i < stockArray.length(); i++) {
                    processStockItem(stockArray.getJSONObject(i), stocksMap);
                }
            } else if (stockData instanceof JSONObject) {
                processStockItem((JSONObject) stockData, stocksMap);
            }
        }
        return stocksMap;
    }

    private void processStockItem(JSONObject stockItem, Map<String, Double> stocksMap) throws JSONException {
        String sucursal = stockItem.getString("Sucursal");
        double stock = stockItem.optDouble("StockDisponible", 0);
        stocksMap.put(sucursal, stock);
    }

    private void updateUIWithBienes(List<Bien> bienes) {
        if (!isAdded() || bienes == null || bienes.isEmpty()) {
            Log.w("UI_UPDATE", "No hay bienes para mostrar");
            return;
        }

        // 1. Agrupar stocks por talla
        Map<String, Double> stockPorTalla = new LinkedHashMap<>();
        boolean hasSizes = false;
        for (Bien bien : bienes) {
            try {
                String[] codigoParts = bien.getCodigo().split(",");
                if (codigoParts.length > 1) {
                    hasSizes = true;
                    String talla = codigoParts[1].trim();
                    double stockActual = stockPorTalla.getOrDefault(talla, 0.0);
                    double stockBien = bien.getStocks().getOrDefault(CodSucursal, 0.0);
                    stockPorTalla.put(talla, stockActual + stockBien);
                }
            } catch (Exception e) {
                Log.e("PROCESS_TALLA", "Error procesando talla", e);
            }
        }

        // 2. Crear lista de items para el RecyclerView
        List<SizeItem> sizeItems = new ArrayList<>();
        if (hasSizes) {
            for (Map.Entry<String, Double> entry : stockPorTalla.entrySet()) {
                String talla = entry.getKey();
                double stock = entry.getValue();
                boolean disponible = stock > 0;
                sizeItems.add(new SizeItem(talla, stock, disponible));
            }
        }

        // 3. Actualizar el RecyclerView y la visibilidad de las vistas de talla
        sizeButtonAdapter.setSizeItems(sizeItems);
        if (sizeItems.isEmpty()) {
            textViewTalla.setVisibility(View.GONE);
            recyclerViewSizes.setVisibility(View.GONE);
            adjustRecyclerViewHeight(0);
            selectedSize = "única"; // Default value for products without size
        } else {
            textViewTalla.setVisibility(View.VISIBLE);
            recyclerViewSizes.setVisibility(View.VISIBLE);
            adjustRecyclerViewHeight(sizeItems.size());
            selectedSize = null; // Reset selection
        }

        // Mostrar descripción
        textDescripcion.setText(bienes.get(0).getDescripcionLarga());

        // Mostrar elementos de cantidad y botón agregar después de búsqueda exitosa
        textView2.setVisibility(View.VISIBLE);
        numberSpinner.setVisibility(View.VISIBLE);
        buttonAddProduct.setVisibility(View.VISIBLE);
    }


    private void adjustRecyclerViewHeight(int itemCount) {
        if (recyclerViewSizes == null || getContext() == null) return;

        // Calcular número de filas (4 columnas por fila)
        int rows = (int) Math.ceil((double) itemCount / 4);

        // Altura aproximada de cada botón + márgenes (en dp)
        int itemHeightDp = 48; // Altura típica de un botón
        int marginDp = 8; // Margen entre elementos

        // Convertir dp a pixels
        float density = getResources().getDisplayMetrics().density;
        int itemHeightPx = (int) (itemHeightDp * density);
        int marginPx = (int) (marginDp * density);

        // Calcular altura total necesaria
        int totalHeightPx = (rows * itemHeightPx) + ((rows - 1) * marginPx);

        // Agregar padding del RecyclerView
        int paddingPx = (int) (10 * density); // 5dp padding * 2
        totalHeightPx += paddingPx;

        // Limitar a máximo 380dp
        int maxHeightPx = (int) (340 * density);
        int finalHeightPx = Math.min(totalHeightPx, maxHeightPx);

        // Aplicar la altura calculada
        ViewGroup.LayoutParams layoutParams = recyclerViewSizes.getLayoutParams();
        layoutParams.height = finalHeightPx;
        recyclerViewSizes.setLayoutParams(layoutParams);
    }

    private void sumarStocks(Bien destino, Bien fuente) {
        for (Map.Entry<String, Double> entry : fuente.getStocks().entrySet()) {
            String sucursal = entry.getKey();
            double stock = entry.getValue();

            destino.getStocks().merge(sucursal, stock, Double::sum);
        }
    }

    private void handleApiError(String error, String responseData) {
        Log.e("API_ERROR", error + " - Response: " + responseData);
        requireActivity().runOnUiThread(() -> {
            progressBarInput.setVisibility(View.GONE);
            showErrorDialog(error);

            if (responseData.contains("null") || responseData.contains("found")) {
                clearProductCodeField();
            }
        });
    }

    private void showErrorDialog(String message) {
        if (!isAdded()) return;

        new AlertDialog.Builder(requireContext())
                .setTitle("Error")
                .setMessage(message)
                .setPositiveButton("Aceptar", null)
                .show();
    }

    // Clase auxiliar para manejar los datos de Bien
    private static class Bien {
        private String codigo;
        private String descripcion;
        private String descripcionLarga;
        private Map<String, Double> stocks; // Sucursal -> Stock

        public Bien(String codigo, String descripcion, String descripcionLarga, Map<String, Double> stocks) {
            this.codigo = codigo;
            this.descripcion = descripcion;
            this.descripcionLarga = descripcionLarga;
            this.stocks = stocks != null ? stocks : new HashMap<>();
        }

        // Getters
        public String getCodigo() { return codigo; }
        public String getDescripcion() { return descripcion; }
        public String getDescripcionLarga() { return descripcionLarga; }
        public Map<String, Double> getStocks() { return stocks; }

        // Método para sumar stocks de otro Bien
        public void sumarStocks(Bien otro) {
            otro.getStocks().forEach((sucursal, stock) ->
                    this.stocks.merge(sucursal, stock, Double::sum)
            );
        }
    }

    private void fetchPrice(OkHttpClient client, String token, String productCode) {
        Log.d("fetchPrice", "Llamada realizada con productCode: " + productCode);

        if (!isAdded() || isDetached()) {
            return;
        }

        // Mostrar loading en el hilo UI
        requireActivity().runOnUiThread(() -> progressBarInput.setVisibility(View.VISIBLE));

        try {
            // Construir el cuerpo de la solicitud
            JSONObject requestBodyJson = new JSONObject();
            JSONObject header = new JSONObject();
            header.put("etiqueta", "consultaGral");
            header.put("codemp", CodEmpresa);
            header.put("codsuc", CodSucursal);
            header.put("fecha", fechaFormateada);

            JSONArray filtrosAdicionales = new JSONArray();
            JSONObject filtro = new JSONObject();
            filtro.put("tagEntidad", "BIEN");
            filtro.put("nombreCampo", "Codigo");
            filtro.put("comparacion", "8");
            filtro.put("Valor", productCode + "0,");
            filtrosAdicionales.put(filtro);

            JSONObject consultaGral = new JSONObject();
            consultaGral.put("filtrosAdicionales", filtrosAdicionales);
            consultaGral.put("incluyeDatosSecundarios", "S");

            requestBodyJson.put("header", header);
            requestBodyJson.put("consultaGral", consultaGral);

            // Construir la solicitud HTTP
            Request request = new Request.Builder()
                    .url(baseUrl + "/api/CONSULTAGRAL/ListaPrecio")
                    .header("Authorization", "Bearer " + token)
                    .post(RequestBody.create(requestBodyJson.toString(), MediaType.parse("application/json")))
                    .build();

            // Configurar cliente con interceptor y timeouts extendidos
            OkHttpClient priceClient = client.newBuilder()
                    .connectTimeout(30, TimeUnit.SECONDS)
                    .readTimeout(30, TimeUnit.SECONDS)
                    .writeTimeout(30, TimeUnit.SECONDS)
                    .addInterceptor(new TokenRefreshInterceptor(token, requireContext()))
                    .build();

            // Realizar la llamada
            priceClient.newCall(request).enqueue(new Callback() {
                @Override
                public void onFailure(Call call, IOException e) {
                    handlePriceError("Error de conexión: " + e.getMessage());
                }

                @Override
                public void onResponse(Call call, Response response) throws IOException {
                    try {
                        if (response.code() == 401) {
                            // Token expirado, intentar refrescar
                            refreshTokenAndRetry(productCode);
                            return;
                        }

                        if (!response.isSuccessful()) {
                            handlePriceError("Error del servidor: " + response.code());
                            return;
                        }

                        String responseData = response.body().string();
                        Log.d("fetchPrice", "Respuesta del servidor: " + responseData);
                        processPriceResponse(responseData);

                    } catch (Exception e) {
                        handlePriceError("Error procesando respuesta: " + e.getMessage());
                    } finally {
                        response.close();
                    }
                }
            });

        } catch (JSONException e) {
            handlePriceError("Error al crear solicitud: " + e.getMessage());
        }
    }

    // Interceptor para manejar tokens expirados
    private class TokenRefreshInterceptor implements Interceptor {
        private final String currentToken;
        private final Context context;

        // Constructor que recibe el token actual y el contexto
        public TokenRefreshInterceptor(String token, Context context) {
            this.currentToken = token;
            this.context = context.getApplicationContext(); // Usar ApplicationContext para evitar memory leaks
        }

        @Override
        public Response intercept(Chain chain) throws IOException {
            Request request = chain.request();
            Response response = chain.proceed(request);

            if (response.code() == 401) {
                response.close();

                // Obtener el token actualizado
                String latestToken = TokenManager.getInstance().getToken();

                // Verificar si el token ha cambiado
                if (latestToken != null && !latestToken.equals(currentToken)) {
                    // Crear nueva solicitud con el token actualizado
                    Request newRequest = request.newBuilder()
                            .header("Authorization", "Bearer " + latestToken)
                            .build();
                    return chain.proceed(newRequest);
                }
            }
            return response;
        }
    }

    public interface TokenRefreshCallback {
        void onTokenRefreshed(String newToken);
        void onTokenRefreshFailed(String error);
    }

    // 1. Define tu propia interfaz de callback
    private interface PriceTokenRefreshCallback {
        void onSuccess(String newToken);
        void onFailure(String error);
    }

    // 2. Método para refrescar token
    private void refreshTokenForPrice(PriceTokenRefreshCallback callback) {
        TokenManager tokenManager = TokenManager.getInstance();

        try {
            // Intenta obtener un nuevo token
            String newToken = tokenManager.getToken(); // o tokenManager.refreshTokenSync()

            if (newToken != null && !newToken.isEmpty()) {
                callback.onSuccess(newToken);
            } else {
                callback.onFailure("Token vacío o nulo");
            }
        } catch (Exception e) {
            callback.onFailure(e.getMessage());
        }
    }

    // 3. Uso en refreshTokenAndRetry
    private void refreshTokenAndRetry(String productCode) {
        refreshTokenForPrice(new PriceTokenRefreshCallback() {
            @Override
            public void onSuccess(String newToken) {
                fetchPrice(new OkHttpClient(), newToken, productCode);
            }

            @Override
            public void onFailure(String error) {
                handlePriceError("Error renovando sesión: " + error);
            }
        });
    }

    private JSONObject buildPriceRequestBody(String productCode) throws JSONException {
        JSONObject requestBodyJson = new JSONObject();
        JSONObject header = new JSONObject();
        header.put("etiqueta", "consultaGral");
        header.put("codemp", CodEmpresa);
        header.put("codsuc", CodSucursal);
        header.put("fecha", fechaFormateada);

        JSONArray filtrosAdicionales = new JSONArray();
        JSONObject filtro = new JSONObject();
        filtro.put("tagEntidad", "BIEN");
        filtro.put("nombreCampo", "Codigo");
        filtro.put("comparacion", "8");
        filtro.put("Valor", productCode + "0,");
        filtrosAdicionales.put(filtro);

        JSONObject consultaGral = new JSONObject();
        consultaGral.put("filtrosAdicionales", filtrosAdicionales);
        consultaGral.put("incluyeDatosSecundarios", "S");

        requestBodyJson.put("header", header);
        requestBodyJson.put("consultaGral", consultaGral);

        return requestBodyJson;
    }



    private void handlePriceError(String error) {
        Log.e("fetchPrice", error);
        requireActivity().runOnUiThread(() -> {
            progressBarInput.setVisibility(View.GONE);
            if (isAdded()) {
                textPrecio.setText("");
                //Toast.makeText(getContext(), "Error al obtener precio: " + error, Toast.LENGTH_LONG).show();
            }
        });
    }

    private void processPriceResponse(String responseData) throws JSONException {
        JSONObject jsonResponse = new JSONObject(responseData);

        if (!jsonResponse.has("BIENES")) {
            handlePriceError("Formato de respuesta inválido");
            return;
        }

        JSONObject bienes = jsonResponse.getJSONObject("BIENES");
        JSONArray bienArray = bienes.getJSONArray("Bien");

        if (bienArray == null || bienArray.length() == 0) {
            handlePriceError("No se encontraron precios");
            return;
        }

        // Tomar el primer bien
        JSONObject primerBien = bienArray.getJSONObject(0);

        // Obtener el valor de I13 de los atributos
        int valorI13 = 0; // Valor por defecto (sin descuento)
        if (primerBien.has("AtributosValores")) {
            JSONObject atributosValores = primerBien.getJSONObject("AtributosValores");
            JSONArray atributos = atributosValores.getJSONArray("Atributo");

            for (int i = 0; i < atributos.length(); i++) {
                JSONObject atributo = atributos.getJSONObject(i);
                if (atributo.getString("Codigo").equals("I13")) {
                    try {
                        valorI13 = Integer.parseInt(atributo.getString("Valor"));
                    } catch (NumberFormatException e) {
                        Log.e("AttributeParse", "Error parsing I13 value", e);
                    }
                    break;
                }
            }
        }

        if (!primerBien.has("Precios")) {
            handlePriceError("No se encontraron precios para el producto");
            return;
        }

        JSONObject precios = primerBien.getJSONObject("Precios");
        JSONArray listaPrecios = precios.getJSONArray("ListaPrecio");

        // Buscar el precio base
        double precioBase = 0;
        String descripcionPrecio = "Precio estándar";

        for (int i = 0; i < listaPrecios.length(); i++) {
            JSONObject precioItem = listaPrecios.getJSONObject(i);
            try {
                if (precioItem.has("Precio")) {
                    precioBase = precioItem.getDouble("Precio");
                    descripcionPrecio = precioItem.optString("DescripcionLista", "Precio estándar");
                    break; // Tomamos el primer precio válido
                }
            } catch (JSONException e) {
                Log.e("PriceParse", "Error parsing price at index " + i, e);
            }
        }

        double descuento = calculateDiscount(valorI13);
        double precioFinal = precioBase * (1 - descuento);

        final double finalPrecioOriginal = precioBase;
        final double finalPrecioConDescuento = precioFinal;
        final String finalDescripcion = descripcionPrecio;

        requireActivity().runOnUiThread(() -> {
            progressBarInput.setVisibility(View.GONE);
            if (finalPrecioOriginal > 0) {
                updatePriceUI(finalPrecioOriginal, finalPrecioConDescuento, finalDescripcion);
            } else {
                textPrecio.setText("Precio no disponible");
            }
        });
    }




    private int findI13Value(JSONObject bien) throws JSONException {
        if (bien.has("AtributosValores") && bien.getJSONObject("AtributosValores").has("Atributo")) {
            JSONArray atributos = bien.getJSONObject("AtributosValores").getJSONArray("Atributo");
            for (int k = 0; k < atributos.length(); k++) {
                JSONObject atributo = atributos.getJSONObject(k);
                if (atributo.getString("Codigo").equals("I13")) {
                    return atributo.getInt("Valor");
                }
            }
        }
        return 0;
    }


    private double calculateDiscount(int valorI13) {
        switch (valorI13) {
            case 1: return 0.15;
            case 2: return 0.20;
            case 3: return 0.25;
            case 4: return 0.30;
            case 5: return 0.40;
            case 6: return 0.50;
            case 7: return 0.60;
            case 8: return 0.70;
            case 9: return 0.65;
            case 10: return 1.00;
            case 11: return 0.10;
            case 12: return 0.35;
            case 13: return 0.45;
            case 14: return 0.55;
            case 15: return 0.51;
            default: return 0;
        }
    }

    private void updatePriceUI(double precioOriginal, double precioConDescuento, String descripcionPrecio) {
        if (!isAdded()) return;

        Locale localePY = new Locale("es", "PY");
        NumberFormat numberFormat = NumberFormat.getNumberInstance(localePY);
        numberFormat.setMaximumFractionDigits(0);
        numberFormat.setMinimumFractionDigits(0);

        // Recuperar el valor de la cotizacion del dia
        SharedPreferences prefs = requireContext().getSharedPreferences("ConfigDB", Context.MODE_PRIVATE);
        cotizacion = prefs.getFloat("valor1_indices", 1f); // 1f es el valor por defecto

        double precioOriginalGs = precioOriginal * cotizacion;
        double precioDescuentoGs = precioConDescuento * cotizacion;

        String precioText;

        if (precioConDescuento < precioOriginal) {
            // Mostrar precio tachado (original) y precio con descuento
            precioText = String.format(localePY, "%s: <s>Gs. %s</s> Gs. %s",
                    descripcionPrecio,
                    numberFormat.format(precioOriginalGs),
                    numberFormat.format(precioDescuentoGs));
        } else {
            // Mostrar solo el precio original (sin descuento)
            precioText = String.format(localePY, "%s: Gs. %s",
                    descripcionPrecio,
                    numberFormat.format(precioOriginalGs));
        }

        // Usamos Html.fromHtml para renderizar el texto tachado si hay descuento
        textPrecio.setText(Html.fromHtml(precioText, Html.FROM_HTML_MODE_LEGACY));
    }

    private void showSnackbar(String descripcion) {
        Snackbar snackbar = Snackbar.make(requireView(), "Producto agregado correctamente: " + descripcion, Snackbar.LENGTH_SHORT);
        snackbar.show();
        new Handler().postDelayed(new Runnable() {
            @Override
            public void run() {
                snackbar.dismiss();
            }
        }, 2000);
    }

    private RequestBody buildRequestBody(String productCode) {
        JSONObject requestBodyJson = new JSONObject();
        try {
            JSONObject headerJson = new JSONObject();
            headerJson.put("etiqueta", "CONSULTAGRAL");
            headerJson.put("codemp", CodEmpresa); // Añade el código de la empresa
            headerJson.put("codsuc", CodSucursal); // Añade el código de la sucursal
            headerJson.put("fecha", fechaFormateada); // Actualiza la fecha

            JSONArray filtrosAdicionalesJson = new JSONArray();
            JSONObject filtroJson = new JSONObject();
            filtroJson.put("TagEntidad", "Bien");
            filtroJson.put("NombreCampo", "Codigo");
            filtroJson.put("Comparacion", "8");
            filtroJson.put("Valor", productCode+"0,");
            filtrosAdicionalesJson.put(filtroJson);

            JSONObject consultaGralJson = new JSONObject();
            consultaGralJson.put("FiltrosAdicionales", filtrosAdicionalesJson);

            requestBodyJson.put("header", headerJson);
            requestBodyJson.put("consultaGral", consultaGralJson);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        return RequestBody.create(MediaType.parse("application/json"), requestBodyJson.toString());
    }


    public void showCart(View view) {
        NavController navController = Navigation.findNavController(view);
        navController.navigate(R.id.action_nav_inputProduct_to_carritoFragment);
    }



    private void showStockInOtherBranches(String productCode, String size) {
        Log.d("StockFlow", "Iniciando diálogo para: " + productCode + " - " + size);
        try {
            StockDialogFragment dialogFragment = StockDialogFragment.newInstance(productCode, size);
            dialogFragment.show(getChildFragmentManager(), "StockDialogFragment");
            Log.d("StockFlow", "Diálogo mostrado");
        } catch (Exception e) {
            Log.e("StockFlow", "Error al mostrar diálogo", e);
            Toast.makeText(getContext(), "Error al mostrar disponibilidad", Toast.LENGTH_SHORT).show();
        }
    }

    private void updateButtonStyle(Button button, boolean isSelected, boolean isAvailable) {
        if (isSelected) {
            // Talla seleccionada - AZUL
            button.setBackgroundColor(ContextCompat.getColor(getContext(), R.color.selectedColor));
            button.setTextColor(Color.WHITE);
        } else {
            // Talla no seleccionada - VERDE o ROJO según disponibilidad
            button.setBackgroundColor(
                    isAvailable ? ContextCompat.getColor(getContext(), R.color.availableColor)
                            : ContextCompat.getColor(getContext(), R.color.unavailableColor)
            );
            button.setTextColor(Color.BLACK);
        }
    }

    private void updateButtonStyles() {
        // Actualizar RecyclerView
        if (sizeButtonAdapter != null) {
            sizeButtonAdapter.notifyDataSetChanged();
        }
    }


    private boolean checkFallbackAvailability(String talla) {
        // Datos de fallback temporal - eliminar cuando se resuelva el problema
        Map<String, Boolean> fallback = new HashMap<>();
        fallback.put("S", true);
        fallback.put("M", true);
        fallback.put("L", false);
        // Agregar más tallas según necesidad

        return fallback.getOrDefault(talla, false);
    }

    @Override
    public void onTokenReceived(String token) {
        this.token = token;
        tokenExpirationTime = System.currentTimeMillis() + TimeUnit.MINUTES.toMillis(30); // 30 minutos de validez
        makeApiRequestWithToken(token); // Continúa con la solicitud de la API
        Log.d("Token", token);
    }


    private void fetchCodigoMadre(String scannedCode) {
        if (!isAdded() || isDetached()) return;

        TokenManager tokenManager = TokenManager.getInstance();
        String token = tokenManager.getToken();

        if (token == null) {
            showUiToast("Token no disponible");
            return;
        }

        try {
            JSONObject requestBody = new JSONObject();
            JSONObject header = new JSONObject();
            header.put("etiqueta", "consultaGral");
            header.put("codemp", CodEmpresa);
            header.put("codsuc", CodSucursal);
            header.put("fecha", fechaFormateada);

            JSONArray filtros = new JSONArray();
            JSONObject filtro = new JSONObject();
            filtro.put("tagEntidad", "Bien");
            filtro.put("nombreCampo", "CodigoAlternativo");
            filtro.put("comparacion", "0");
            filtro.put("valor", scannedCode);
            filtros.put(filtro);

            JSONObject consultaGral = new JSONObject();
            consultaGral.put("filtrosAdicionales", filtros);

            requestBody.put("header", header);
            requestBody.put("consultaGral", consultaGral);

            Request request = new Request.Builder()
                    .url(baseUrl + "/api/CONSULTAGRAL/Bien")
                    .header("Authorization", "Bearer " + token)
                    .post(RequestBody.create(requestBody.toString(), MediaType.parse("application/json")))
                    .build();

            new OkHttpClient.Builder()
                    .connectTimeout(15, TimeUnit.SECONDS)
                    .readTimeout(15, TimeUnit.SECONDS)
                    .writeTimeout(15, TimeUnit.SECONDS)
                    .build()
                    .newCall(request)
                    .enqueue(new Callback() {
                        @Override
                        public void onFailure(Call call, IOException e) {
                            showUiToast("Error de conexión: " + e.getMessage());
                        }

                        @Override
                        public void onResponse(Call call, Response response) throws IOException {
                            if (!response.isSuccessful()) {
                                showUiToast("Error del servidor: " + response.code());
                                return;
                            }

                            try {
                                String responseData = response.body().string();
                                JSONObject json = new JSONObject(responseData);

                                if (!json.has("BIENES")) {
                                    showUiToast("Formato de respuesta inválido");
                                    return;
                                }

                                JSONObject bien = json.getJSONObject("BIENES").getJSONObject("Bien");
                                String codigo = extractCodigoMadre(bien.getString("Codigo"));

                                requireActivity().runOnUiThread(() -> {
                                    if (!isAdded()) return;
                                    productCode = codigo;
                                    editTextProductCode.setText(codigo);
                                    fetchPrice(new OkHttpClient(), token, productCode);
                                    makeApiRequestWithToken(token);
                                });

                            } catch (Exception e) {
                                //showUiToast("Error procesando respuesta");
                            }
                        }
                    });

        } catch (JSONException e) {
            showUiToast("Error creando solicitud");
        }
    }

    private String extractCodigoMadre(String fullCode) {
        if (fullCode == null || fullCode.isEmpty()) {
            return ""; // o null según necesites
        }

        // Buscar el índice de "0,"
        int index = fullCode.indexOf("0,");

        if (index > 0) {
            // Extraer todo ANTES del "0" (excluyendo el "0," completamente)
            return fullCode.substring(0, index);
        }

        // Si no encuentra "0,", devolver el código original
        return fullCode;
    }

    private void showUiToast(String message) {
        requireActivity().runOnUiThread(() -> {
            if (isAdded()) Toast.makeText(getContext(), message, Toast.LENGTH_LONG).show();
        });
    }


    // Clase del diálogo para mostrar stock
    public static class StockDialogFragment extends DialogFragment {
        private static final String ARG_PRODUCT_CODE = "productCode";
        private static final String ARG_SIZE = "size";

        private RecyclerView recyclerView;
        private ProgressBar progressBar;
        private StockAdapter adapter;
        private String baseUrl, baseUrl2, codEmpresa2, fechaFormateada;
        private String productCode, size;

        public static StockDialogFragment newInstance(String productCode, String size) {
            StockDialogFragment fragment = new StockDialogFragment();
            Bundle args = new Bundle();
            args.putString(ARG_PRODUCT_CODE, productCode);
            args.putString(ARG_SIZE, size);
            fragment.setArguments(args);
            return fragment;
        }

        @Override
        public void onCreate(@Nullable Bundle savedInstanceState) {
            super.onCreate(savedInstanceState);
            if (getArguments() != null) {
                productCode = getArguments().getString(ARG_PRODUCT_CODE);
                size = getArguments().getString(ARG_SIZE);
            }

            // Obtener configuración
            obtenerConfiguracion();
        }

        @NonNull
        @Override
        public Dialog onCreateDialog(@Nullable Bundle savedInstanceState) {
            AlertDialog.Builder builder = new AlertDialog.Builder(requireActivity());
            View view = LayoutInflater.from(requireContext()).inflate(R.layout.dialog_stock, null);

            recyclerView = view.findViewById(R.id.recyclerViewStock);
            progressBar = view.findViewById(R.id.progressBarStock);

            // Configurar RecyclerView
            recyclerView.setLayoutManager(new LinearLayoutManager(getContext()));
            adapter = new StockAdapter();
            recyclerView.setAdapter(adapter);

            builder.setView(view)
                    .setTitle("Disponibilidad en otras sucursales")
                    .setPositiveButton("Cerrar", (dialog, which) -> dismiss());

            return builder.create();
        }

        @Override
        public void onStart() {
            super.onStart();

        }

        private void obtenerConfiguracion() {
            Executors.newSingleThreadExecutor().execute(() -> {
                AppDatabase db = AppDatabase.getInstance(requireContext());
                Configuracion configuracion = db.configuracionDao().obtenerConfiguracion();

                // Usar valores por defecto si configuracion es null
                String url = configuracion != null ? configuracion.getBaseUrl() : ParametrosConf.APPMOVIL_BASE_URL;
                String url2 = configuracion != null ? configuracion.getBaseUrl2() : ParametrosConf.APPMOVIL_BASE_URL;
                String empresa2 = configuracion != null ? configuracion.getCodigoEmpresa2() : "1";
                String fecha = LocalDate.now().format(DateTimeFormatter.ofPattern("dd-MM-yyyy"));

                requireActivity().runOnUiThread(() -> {
                    this.baseUrl = url;
                    this.baseUrl2 = url2;
                    this.codEmpresa2 = empresa2;
                    this.fechaFormateada = fecha;
                    buscarStockEnOtrasSucursales(); // Mover la llamada aquí
                });
            });
        }

        private void buscarStockEnOtrasSucursales() {
            progressBar.setVisibility(View.VISIBLE);

            TokenManager2 tokenManager2 = TokenManager2.getInstance();
            String token = tokenManager2.getToken();

            if (token == null) {
                tokenManager2.requestToken(requireContext(), () -> {
                    String newToken = tokenManager2.getToken();
                    if (newToken != null) {
                        consultarStockAPI(newToken);
                    } else {
                        mostrarError("Error al obtener token");
                    }
                });
            } else {
                consultarStockAPI(token);
            }
        }

        private void consultarStockAPI(String token) {
            OkHttpClient client = new OkHttpClient.Builder()
                    .connectTimeout(30, TimeUnit.SECONDS)
                    .readTimeout(30, TimeUnit.SECONDS)
                    .writeTimeout(30, TimeUnit.SECONDS)
                    .build();

            RequestBody requestBody = crearRequestBody();
            Request request = new Request.Builder()
                    .url(baseUrl2 + "/api/CONSULTAGRAL/Stock")
                    .header("Authorization", "Bearer " + token)
                    .post(requestBody)
                    .build();

            client.newCall(request).enqueue(new Callback() {
                @Override
                public void onFailure(Call call, IOException e) {
                    requireActivity().runOnUiThread(() -> {
                        progressBar.setVisibility(View.GONE);
                        mostrarError("Error de conexión: " + e.getMessage());
                    });
                }

                @Override
                public void onResponse(Call call, Response response) throws IOException {
                    if (!response.isSuccessful()) {
                        requireActivity().runOnUiThread(() -> {
                            progressBar.setVisibility(View.GONE);
                            mostrarError("Error en el servidor: " + response.code());
                        });
                        return;
                    }

                    String responseData = response.body().string();
                    procesarRespuesta(responseData);
                }
            });
        }

        private RequestBody crearRequestBody() {
            try {
                if (baseUrl2 == null || codEmpresa2 == null || fechaFormateada == null) {
                    throw new IllegalStateException("Configuración no cargada");
                }

                JSONObject body = new JSONObject();
                JSONObject header = new JSONObject();
                header.put("etiqueta", "CONSULTAGRAL");
                header.put("codemp", codEmpresa2);
                header.put("fecha", fechaFormateada);

                JSONArray filtros = new JSONArray();
                JSONObject filtro = new JSONObject();
                filtro.put("TagEntidad", "Bien");
                filtro.put("NombreCampo", "Codigo");
                filtro.put("Comparacion", "0");
                filtro.put("Valor", productCode);
                filtros.put(filtro);

                body.put("header", header);
                body.put("consultaGral", new JSONObject().put("FiltrosAdicionales", filtros));

                return RequestBody.create(body.toString(), MediaType.parse("application/json"));
            } catch (JSONException e) {
                Log.e("StockDialog", "Error al crear JSON", e);
                return null;
            }
        }

        private void procesarRespuesta(String responseData) {
            try {
                Log.d("StockDialog", "Respuesta completa: " + responseData);

                JSONObject jsonResponse = new JSONObject(responseData);
                JSONObject cuerpo = jsonResponse.getJSONObject("Cuerpo");
                JSONArray bienesArray = cuerpo.getJSONArray("BIENES");
                List<ItemStock> items = new ArrayList<>();

                // Recorremos todos los bienes (en este caso parece haber solo uno)
                for (int i = 0; i < bienesArray.length(); i++) {
                    JSONObject bien = bienesArray.getJSONObject(i);

                    if (bien.has("Stocks")) {
                        JSONArray stocksArray = bien.getJSONArray("Stocks");
                        for (int j = 0; j < stocksArray.length(); j++) {
                            agregarItemStock(items, stocksArray.getJSONObject(j));
                        }
                    }
                }

                requireActivity().runOnUiThread(() -> {
                    progressBar.setVisibility(View.GONE);
                    if (items.isEmpty()) {
                        mostrarError("No hay stock disponible en otras sucursales");
                    } else {
                        adapter.setItems(items);
                    }
                });

            } catch (JSONException e) {
                Log.e("StockDialog", "Error al parsear JSON", e);
                mostrarError("Formato de respuesta inesperado");
            }
        }




        private void agregarItemStock(List<ItemStock> items, JSONObject stockObj) throws JSONException {
            try {
                // Manejar el StockDisponible que viene como String
                String stockStr = stockObj.getString("StockDisponible");
                double stock = Double.parseDouble(stockStr);

                String sucursal = stockObj.getString("Deposito");
                String nombreSucursal = stockObj.optString("DescripcionDeposito", "Sucursal " + sucursal);
                if (stockObj.getString("Empresa").equals(codEmpresa2)) {
                    if (stockObj.getString("Talle").equals(size)) {
                        if (stock > 0) {
                            items.add(new ItemStock(sucursal, nombreSucursal, stock));
                        }
                    }
                }
            } catch (NumberFormatException e) {
                Log.e("StockDialog", "Error al parsear StockDisponible", e);
            } catch (JSONException e) {
                Log.e("StockDialog", "Error procesando item de stock", e);
            }
        }

        private void mostrarError(String mensaje) {
            requireActivity().runOnUiThread(() -> {
                progressBar.setVisibility(View.GONE);
                if (getContext() != null && isAdded()) {
                    new AlertDialog.Builder(getContext())
                            .setTitle("Error")
                            .setMessage(mensaje + "\n\nCódigo: " + productCode + "\nTalla: " + size)
                            .setPositiveButton("Aceptar", null)
                            .show();
                }
            });
        }

        private static class StockAdapter extends RecyclerView.Adapter<StockAdapter.ViewHolder> {
            private List<ItemStock> items = new ArrayList<>();

            void setItems(List<ItemStock> newItems) {
                this.items = new ArrayList<>(newItems);
                notifyDataSetChanged();
            }

            @NonNull
            @Override
            public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
                View view = LayoutInflater.from(parent.getContext())
                        .inflate(R.layout.item_stock, parent, false);
                return new ViewHolder(view);
            }

            @Override
            public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
                ItemStock item = items.get(position);
                holder.tvSucursal.setText(item.sucursal);
                holder.tvNombreSucursal.setText(item.nombreSucursal);
                holder.tvStock.setText(String.valueOf(item.stock));
            }

            @Override
            public int getItemCount() {
                return items.size();
            }

            static class ViewHolder extends RecyclerView.ViewHolder {
                final TextView tvSucursal, tvNombreSucursal, tvTalla, tvStock;

                ViewHolder(View itemView) {
                    super(itemView);
                    tvSucursal = itemView.findViewById(R.id.tvSucursal);
                    tvNombreSucursal = itemView.findViewById(R.id.tvNombreSucursal);
                    tvTalla = itemView.findViewById(R.id.tvTalla);
                    tvStock = itemView.findViewById(R.id.tvStock);
                }
            }
        }

        private static class ItemStock {
            final String sucursal, nombreSucursal;
            final double stock;

            ItemStock(String sucursal, String nombreSucursal, double stock) {
                this.sucursal = sucursal;
                this.nombreSucursal = nombreSucursal;
                this.stock = stock;
            }
        }
    }

    private double getStockForSelectedSize() {
        if (bienesList == null || selectedSize == null) return 0;

        double stockTotal = 0;
        // If no sizes, sum all stock for the product
        if (selectedSize.equals("única")) {
            for (Bien bien : bienesList) {
                stockTotal += bien.getStocks().getOrDefault(CodSucursal, 0.0);
            }
            return stockTotal;
        }

        // If there are sizes, get stock for the selected size
        for (Bien bien : bienesList) {
            String[] codigoParts = bien.getCodigo().split(",");
            if (codigoParts.length > 1 && codigoParts[1].trim().equals(selectedSize)) {
                stockTotal += bien.getStocks().getOrDefault(CodSucursal, 0.0);
            }
        }
        return stockTotal;
    }

    private void showStockExceededDialog(double stockDisponible) {
        new AlertDialog.Builder(requireContext())
                .setTitle("Stock insuficiente")
                .setMessage(String.format(Locale.getDefault(),
                        "La cantidad seleccionada supera el stock disponible (%d unidades). " +
                                "Por favor, seleccione una cantidad menor.", (int) stockDisponible))
                .setPositiveButton("Aceptar", null)
                .show();
    }

    // Adaptador para el RecyclerView de tallas
    private class SizeButtonAdapter extends RecyclerView.Adapter<SizeButtonAdapter.SizeViewHolder> {
        private List<SizeItem> sizeItems = new ArrayList<>();

        public void setSizeItems(List<SizeItem> items) {
            this.sizeItems = items;
            notifyDataSetChanged();
        }

        @NonNull
        @Override
        public SizeViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
            View view = LayoutInflater.from(parent.getContext())
                    .inflate(R.layout.item_size_button, parent, false);
            return new SizeViewHolder(view);
        }

        @Override
        public void onBindViewHolder(@NonNull SizeViewHolder holder, int position) {
            SizeItem item = sizeItems.get(position);
            holder.bind(item);
        }

        @Override
        public int getItemCount() {
            return sizeItems.size();
        }

        class SizeViewHolder extends RecyclerView.ViewHolder {
            private Button btnTalla;

            public SizeViewHolder(@NonNull View itemView) {
                super(itemView);
                btnTalla = itemView.findViewById(R.id.btnTalla);
            }

            public void bind(SizeItem item) {
                btnTalla.setText(item.talla);
                btnTalla.setTag(R.id.tag_talla, item.talla);
                btnTalla.setTag(R.id.tag_stock, item.stock);

                // Configurar comportamiento según disponibilidad
                if (item.disponible) {
                    btnTalla.setOnClickListener(v -> {
                        selectedSize = item.talla;
                        updateButtonStyles();
                    });
                } else {
                    btnTalla.setOnClickListener(v -> showStockInOtherBranches(productCode, item.talla));
                }

                // Aplicar estilo
                updateButtonStyle(btnTalla, item.talla.equals(selectedSize), item.disponible);
            }
        }
    }

    // Clase para representar un item de talla
    private static class SizeItem {
        String talla;
        double stock;
        boolean disponible;

        SizeItem(String talla, double stock, boolean disponible) {
            this.talla = talla;
            this.stock = stock;
            this.disponible = disponible;
        }
    }

}
